# <PERSON><PERSON> - Portfolio Website

A professional, responsive portfolio website built with HTML, CSS (Bootstrap), and JavaScript.

## Features

- **Responsive Design**: Works perfectly on all devices (desktop, tablet, mobile)
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Interactive Elements**: Smooth scrolling, hover effects, and dynamic navigation
- **Bootstrap Integration**: Utilizes Bootstrap 5 for responsive grid and components
- **Font Awesome Icons**: Professional icons throughout the site
- **Google Fonts**: Modern typography with Poppins font family

## Sections

1. **Hero Section**: Professional introduction with profile image
2. **About/Profile**: Personal statement and career objectives
3. **Education**: Complete educational background with timeline design
4. **Skills**: Professional skills and languages
5. **Volunteer Projects**: Key projects and contributions
6. **Contact**: Contact information and social links

## Technologies Used

- HTML5
- CSS3 (with custom animations and responsive design)
- Bootstrap 5.3.0
- JavaScript (ES6+)
- Font Awesome 6.4.0
- Google Fonts (Poppins)

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # Custom CSS styles
├── script.js           # JavaScript functionality
├── profile-placeholder.jpg  # Profile image (to be replaced)
└── README.md           # This file
```

## Setup Instructions

1. **Clone or Download**: Get all the files in a single directory
2. **Replace Profile Image**: Replace `profile-placeholder.jpg` with your actual profile photo
3. **Customize Content**: Update any personal information in `index.html` if needed
4. **Open in Browser**: Simply open `index.html` in any modern web browser

## Customization

### Colors
The website uses CSS custom properties (variables) for easy color customization:
- `--primary-color`: Main brand color (default: #2c3e50)
- `--secondary-color`: Secondary brand color (default: #3498db)
- `--accent-color`: Accent color (default: #e74c3c)

### Content
All content can be easily modified in the `index.html` file:
- Personal information
- Education details
- Skills and languages
- Project descriptions
- Contact information

### Styling
Custom styles are in `styles.css` and can be modified:
- Typography
- Colors and gradients
- Animations and transitions
- Responsive breakpoints

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers

## Performance Features

- Optimized images and assets
- Smooth scrolling with performance throttling
- Lazy loading animations
- Minimal JavaScript for fast loading

## Contact Information

Based on the resume provided:
- **Phone**: +94 76 072 4138
- **Email**: <EMAIL>
- **Address**: 41,Ihala Yagoda, New Road, Gampaha
- **LinkedIn**: www.linkedin.com/in/hansikasamarasinghe

## License

This portfolio template is free to use and modify for personal purposes.

---

**Note**: Remember to replace the placeholder profile image with your actual photo for the complete professional look!
