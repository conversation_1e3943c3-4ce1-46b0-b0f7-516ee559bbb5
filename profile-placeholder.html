<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Placeholder</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .profile-placeholder {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }
        
        .profile-placeholder::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="35" r="15" fill="rgba(255,255,255,0.3)"/><path d="M20 80 Q50 60 80 80 L80 100 L20 100 Z" fill="rgba(255,255,255,0.3)"/></svg>');
            background-size: cover;
            background-position: center;
        }
        
        .initials {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 10px;
            z-index: 2;
            position: relative;
        }
        
        .name {
            font-size: 1.2rem;
            font-weight: 500;
            z-index: 2;
            position: relative;
        }
        
        .instruction {
            margin-top: 30px;
            text-align: center;
            color: #666;
            max-width: 400px;
        }
        
        .instruction h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .instruction p {
            line-height: 1.6;
            margin-bottom: 10px;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div>
        <div class="profile-placeholder">
            <div class="initials">HS</div>
            <div class="name">Hansika Samarasinghe</div>
        </div>
        
        <div class="instruction">
            <h3>Profile Image Placeholder</h3>
            <p>This is a temporary placeholder for the profile image.</p>
            <p>To complete your portfolio:</p>
            <div class="highlight">
                <strong>Replace this placeholder:</strong><br>
                1. Take a professional headshot photo<br>
                2. Save it as "profile-placeholder.jpg"<br>
                3. Replace the existing placeholder file<br>
                4. The image should be square (300x300px recommended)
            </div>
        </div>
    </div>
</body>
</html>
