/* Custom CSS for <PERSON><PERSON> Portfolio */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Custom Colors */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-bg: #f8f9fa;
    --dark-bg: #2c3e50;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #ffd700 !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.profile-image-container {
    position: relative;
    z-index: 2;
}

.profile-image {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    border: 8px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    object-fit: cover;
    transition: transform 0.3s ease;
}

.profile-image:hover {
    transform: scale(1.05);
}

/* Section Styling */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    margin: 0 auto 2rem;
    border-radius: 2px;
}

/* Timeline */
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--secondary-color);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin: 40px 0;
    width: 50%;
}

.timeline-item:nth-child(odd) {
    left: 0;
    padding-right: 40px;
    text-align: right;
}

.timeline-item:nth-child(even) {
    left: 50%;
    padding-left: 40px;
    text-align: left;
}

.timeline-item::before {
    content: '';
    position: absolute;
    top: 20px;
    width: 20px;
    height: 20px;
    background: var(--secondary-color);
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 0 0 4px var(--secondary-color);
}

.timeline-item:nth-child(odd)::before {
    right: -10px;
}

.timeline-item:nth-child(even)::before {
    left: -10px;
}

.timeline-content {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.timeline-date {
    background: var(--accent-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Skills Section */
.skill-category {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: 100%;
    transition: transform 0.3s ease;
}

.skill-category:hover {
    transform: translateY(-5px);
}

.skill-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    font-size: 1.1rem;
    font-weight: 500;
    border-bottom: 1px solid #eee;
}

.skill-item:last-child {
    border-bottom: none;
}

/* Project Cards */
.project-card .card {
    border: none;
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card .card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.card-title {
    color: var(--primary-color);
    font-weight: 600;
}

/* Contact Section */
.contact-item {
    padding: 20px;
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
}

.contact-item i {
    color: rgba(255,255,255,0.8);
    margin-bottom: 15px;
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .timeline::before {
        left: 20px;
    }
    
    .timeline-item {
        width: 100%;
        left: 0 !important;
        padding-left: 50px !important;
        padding-right: 0 !important;
        text-align: left !important;
    }
    
    .timeline-item::before {
        left: 10px !important;
    }
    
    .profile-image {
        width: 250px;
        height: 250px;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .profile-image {
        width: 200px;
        height: 200px;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .timeline-content {
        padding: 20px;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Background Pattern */
.bg-pattern {
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px);
    background-size: 50px 50px;
}
