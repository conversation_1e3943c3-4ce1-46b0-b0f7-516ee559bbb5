<svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3498db;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="150" cy="150" r="150" fill="url(#bg)"/>
  
  <!-- Person Icon -->
  <g fill="rgba(255,255,255,0.8)">
    <!-- Head -->
    <circle cx="150" cy="110" r="35"/>
    <!-- Body -->
    <path d="M80 250 Q150 200 220 250 L220 300 L80 300 Z"/>
  </g>
  
  <!-- Initials -->
  <text x="150" y="160" font-family="Arial, sans-serif" font-size="48" font-weight="bold" 
        text-anchor="middle" fill="white" opacity="0.9">HS</text>
  
  <!-- Name -->
  <text x="150" y="280" font-family="Arial, sans-serif" font-size="14" font-weight="500" 
        text-anchor="middle" fill="white" opacity="0.8">Hansika Samarasinghe</text>
</svg>
